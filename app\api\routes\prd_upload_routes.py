from fastapi import APIRouter, Depends, UploadFile, File,HTTPException,status,Request,Form
from typing import List
from app.database.session import get_db
from app.database.dependencies import optional_get_current_user
from app.services.prd_upload_service import PrdUploadService
from sqlalchemy.orm import Session
from typing import Optional
from app.database.models.user_model import User
from app.services.cache_services import CacheService


router = APIRouter()


@router.post("/upload_files")
def process_uploaded_files(request: Request,user_input: Optional[str] = Form(None),current_user: Optional[User] = Depends(optional_get_current_user),
                           files: List[UploadFile] = File(None),session_id: Optional[str] = None,db: Session = Depends(get_db)):
    try:
        client_ip = request.client.host

        if not current_user:
            PrdUploadService.handle_guest_user(db, client_ip)
        else:
           PrdUploadService.validate_authenticated_user(db, current_user)

        session_id = PrdUploadService.handle_session(db, session_id, current_user, files)
        CacheService.invalidate_chat_cache(current_user.id)
        CacheService.invalidate_chat_data_cache(current_user.id, session_id)

        result = PrdUploadService.process_uploaded_files(
            user_id=current_user.id if current_user else None,
            user_input=user_input,
            files=files,
            session_id=session_id,
            db=db
        )       

        if current_user and current_user.role.name == "User":
            PrdUploadService.decrement_user_upload_count(db, current_user)

        return {"message": "Files processed successfully", "data": result}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing files: {str(e)}"
        )
