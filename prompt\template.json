{"name": null, "input_variables": ["extracted_text", "persona", "user_instruction"], "optional_variables": [], "output_parser": null, "partial_variables": {}, "metadata": null, "tags": null, "template": "\n{persona}\n\nYou are tasked with generating a comprehensive and detailed Project Requirement Document (PRD) based on the extracted information and any optional user instructions provided.\n\nExtracted Input:\n{extracted_text}\n\nAdditional User Instructions:\n{user_instruction}\n\nGenerate a DETAILED and COMPREHENSIVE PRD with the following sections. Each section should be thorough and well-developed:\n\n1. **Project Title** - Clear, descriptive title\n2. **Project Overview** - Detailed description (3-4 paragraphs minimum)\n3. **Business Objectives** - Multiple specific, measurable objectives with clear business value\n4. **Functional Requirements** - Comprehensive list with detailed descriptions, user stories, and acceptance criteria\n5. **Non-Functional Requirements** - Detailed performance, security, scalability, and usability requirements\n6. **Stakeholders** - Complete stakeholder analysis with roles and responsibilities\n7. **Timeline and Milestones** - Detailed project phases with specific deliverables and dates\n8. **Assumptions and Constraints** - Thorough analysis of project assumptions and limitations\n9. **Risks and Mitigation** - Comprehensive risk assessment with detailed mitigation strategies\n10. **Conclusion or Recommendations** - Detailed recommendations and next steps\n\nIMPORTANT GUIDELINES:\n- Make each section substantial and detailed (not just bullet points)\n- For video content, extract and utilize ALL relevant information\n- Include specific technical details, user scenarios, and implementation considerations\n- Provide comprehensive analysis rather than brief summaries\n- Ensure the PRD is thorough enough for actual project implementation\n- Use the full context from the extracted content to create a rich, detailed document\n\nBe accurate, professional, and comprehensive. Use ALL available information from the extracted text to create a detailed PRD.\n", "template_format": "f-string", "validate_template": true, "_type": "prompt"}