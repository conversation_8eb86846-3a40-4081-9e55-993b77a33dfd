{"name": null, "input_variables": ["extracted_text", "persona", "user_instruction"], "optional_variables": [], "output_parser": null, "partial_variables": {}, "metadata": null, "tags": null, "template": "\n{persona}\n\nYou are tasked with generating a well-structured and professional Project Requirement Document (PRD) based on the extracted information and any optional user instructions provided.\n\nExtracted Input:\n{extracted_text}\n\nAdditional User Instructions:\n{user_instruction}\n\nGenerate a comprehensive PRD with the following sections:\n\n1. **Project Title** - Clear, descriptive title\n2. **Project Overview** - Detailed description of the project scope and purpose\n3. **Business Objectives** - Specific, measurable objectives with clear business value\n4. **Functional Requirements** - Detailed list of features and capabilities with descriptions\n5. **Non-Functional Requirements** - Performance, security, scalability, and usability requirements\n6. **Stakeholders** - Key stakeholders and their roles\n7. **Timeline and Milestones** - Project phases with deliverables and estimated timelines\n8. **Assumptions and Constraints** - Key assumptions and project limitations\n9. **Risks and Mitigation** - Potential risks and mitigation strategies\n10. **Conclusion or Recommendations** - Summary and recommended next steps\n\nGuidelines:\n- Make each section substantial and informative\n- Extract and utilize all relevant information from the provided content\n- Include specific details and implementation considerations where applicable\n- Ensure the PRD provides clear direction for project implementation\n- Be accurate, professional, and thorough without being overly verbose\n\nUse all available information from the extracted text to create a comprehensive and actionable PRD.\n", "template_format": "f-string", "validate_template": true, "_type": "prompt"}