from pydantic_settings import BaseSettings
from pydantic import Field
from typing import ClassVar
import os

class Settings(BaseSettings):
    DATABASE_URL:str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 1440
    SECRET_KEY:str
    ALGORITHM: str = "HS256"
    SMTP_SERVER: str
    SMTP_PORT: int
    SMTP_EMAIL: str
    SMTP_PASSWORD: str
    API_V1_STR: str = "/api/v1"
    BACKEND_URL: str = "https://prd-api.csdevhub.com"
    #FRONTEND_URL: str = "http://localhost:3045"
    FRONTEND_URL: str = "https://prd.csdevhub.com"
    GOOGLE_CLIENT_ID: str
    GOOGLE_CLIENT_SECRET: str
    REDIRECT_URI:str ="https://prd-api.csdevhub.com/api/v1/OAuth/auth/callback"    
    GOOGLE_SCOPE:str ="https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile"
    GITHUB_CLIENT_ID: str
    GITHUB_CLIENT_SECRET: str
    GITHUB_REDIRECT_URI:str = "https://prd-api.csdevhub.com/api/v1/OAuth/auth/github_callback"
    MICROSOFT_CLIENT_ID: str
    MICROSOFT_CLIENT_SECRET: str
    MICROSOFT_TENANT_ID: str
    MICROSOFT_REDIRECT_URI:str = "https://prd-api.csdevhub.com/api/v1/OAuth/auth/microsoft_callback"
    # MICROSOFT_REDIRECT_URI:str = "http://127.0.0.1:3016/api/v1/OAuth/auth/microsoft_callback"
    STRIPE_PUBLISH_KEY: str
    STRIPE_SECRET_KEY: str
    WEBHOOK_KEY: str
    OPENAI_API_KEY: str
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    NOTION_OAUTH_Client_ID: str
    NOTION_OAuth_Client_Secret: str
    NOTION_REDIRECT_URI: ClassVar[str] = "https://prd-api.csdevhub.com/api/v1/notion/notion_callback"
    NOTION_AUTHORIZATION_URL: ClassVar[str] = (
        "https://api.notion.com/v1/oauth/authorize?"
        "client_id=223d872b-594c-80e8-83cc-00379365f0cd&"
        "response_type=code&owner=user&"
        "redirect_uri=https%3A%2F%2Fprd-api.csdevhub.com%2Fapi%2Fv1%2Fnotion%2Fnotion_callback"
    )
    MAX_UPLOADS_PER_IP: ClassVar[int] = 5
    GUEST_WINDOW_MINUTES: ClassVar[int] = 1440

    OPTIMAL_CHUNK_SIZE: ClassVar[int] = 80 * 1024 * 1024
    MAX_WORKERS: ClassVar[int] = min(8, os.cpu_count() * 2)
    FFMPEG_PRESET: ClassVar[str] = "ultrafast"
    BATCH_SIZE: ClassVar[int] = 8  # Increased from 3 to 8 for better performance
    RETRY_DELAY: ClassVar[int] = 1  # Reduced from 2 to 1 second for faster processing
    NOTION_VERSION: ClassVar[str] = "2022-06-28"
    ATLASSIAN_CLIENT_ID: str
    ATLASSIAN_CLIENT_SECRET: str
    ATLASSIAN_REDIRECT_URI: str
    FERNET_KEY: str
   
    class Config:
        env_file = ".env"
    
settings = Settings()