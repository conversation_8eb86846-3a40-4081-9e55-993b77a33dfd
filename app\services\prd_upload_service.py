import os
import openai
import tempfile
import pytesseract
from PIL import Image
from uuid import uuid4
from typing import List, Tuple
from app.database.models.user_model import User
from app.database.models.user_uploads_model import UserUpload
from app.database.models.guestusage import GuestUsage
from app.core.config import settings
from fastapi import HTTPException, status
from datetime import datetime, timedelta
from app.database.models.chat_session_model import ChatSession
from app.database.models.chat_history_model import Cha<PERSON><PERSON><PERSON><PERSON>
from app.database.models.subscriptions_model import Subscription
from pathlib import Path
from fastapi import UploadFile
from sqlalchemy.orm import Session
from langchain_openai import ChatOpenAI
from .cache_services import CacheService
from langchain_core.prompts import load_prompt
from langchain_community.document_loaders import PyPDFLoader, Docx2txtLoader, TextLoader
import re
from langchain.schema import HumanMessage, AIMessage 
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.schema.output_parser import Str<PERSON>utputParser
from langchain.schema.messages import BaseMessage
from typing import Optional
from langchain_core.prompts import PromptTemplate
import shutil
import subprocess
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
import time
from tenacity import retry, stop_after_attempt, wait_exponential
import asyncio
import aiofiles
from openai import AsyncOpenAI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PrdUploadService:
    
    OPTIMAL_CHUNK_SIZE = settings.OPTIMAL_CHUNK_SIZE 
    MAX_WORKERS = settings.MAX_WORKERS
    FFMPEG_PRESET = settings.FFMPEG_PRESET
    BATCH_SIZE = settings.BATCH_SIZE
    RETRY_DELAY =settings.RETRY_DELAY
    
    @staticmethod
    def handle_guest_user(db: Session, client_ip: str):
        now = datetime.now()
        usage = db.query(GuestUsage).filter_by(ip_address=client_ip).first()

        if usage:
            if now - usage.last_accessed > timedelta(minutes=settings.GUEST_WINDOW_MINUTES):
                usage.upload_count = 1
            elif usage.upload_count >= settings.MAX_UPLOADS_PER_IP:
                raise HTTPException(
                    status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                    detail="Free usage limit exceeded. Please login to enjoy services."
                )
            else:
                usage.upload_count += 1
            usage.last_accessed = now
        else:
            usage = GuestUsage(ip_address=client_ip, upload_count=1, last_accessed=now)

        db.add(usage)
        db.commit()

    @staticmethod
    def validate_authenticated_user(db: Session, current_user: User):
        user_uploads = db.query(UserUpload).filter(UserUpload.user_id == current_user.id).first()
        subscription = db.query(Subscription).filter(Subscription.user_id == current_user.id).first()

        if not user_uploads:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Uploads not found.")

        if subscription and subscription.expiry_date < datetime.now():
            raise HTTPException(
                status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                detail="Your subscription has expired. Please renew your subscription to use this feature."
            )

        if  user_uploads.remaining_uploads <= 0:
            raise HTTPException(
                status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                detail="Please upgrade your plan to continue using this feature."
            )

    @staticmethod
    def handle_session(db: Session, session_id: Optional[str], current_user: Optional[User], files: List[UploadFile]) -> str:
        user_id = current_user.id if current_user else None

        if session_id is None:
            if not files:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Please upload files to generate PRD")
            session_id = str(uuid4())
            db.add(ChatSession(session_id=session_id, user_id=user_id, created_at=datetime.now(), updated_at=datetime.now()))
            db.commit()
        else:
            existing_history = db.query(ChatHistory).filter_by(session_id=session_id).first()
            if not existing_history and not files:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No files found in session history.")

        return session_id

    @staticmethod
    def decrement_user_upload_count(db: Session, current_user: User):
        user_uploads = db.query(UserUpload).filter(UserUpload.user_id == current_user.id).first()
        if user_uploads and user_uploads.remaining_uploads > 0:
            user_uploads.remaining_uploads -= 1
            db.commit()
            db.refresh(user_uploads) 

    @staticmethod
    def extract_audio_from_video_optimized(video_path: str, output_audio_path: str) -> str:
        """Ultra-optimized audio extraction with better error handling"""
        try:
            if os.path.exists(output_audio_path):
                os.remove(output_audio_path)

            # Optimized FFmpeg command with better settings
            command = [
                "ffmpeg",
                "-i", video_path,
                "-vn",                          # no video
                "-ac", "1",                     # mono channel
                "-ar", "16000",                 # Good balance between quality and speed
                "-acodec", "libmp3lame",        # MP3 codec for smaller files
                "-b:a", "64k",                  # Lower bitrate for speed
                "-f", "mp3",                    # MP3 format (smaller than WAV)
                "-preset", "ultrafast",
                "-threads", str(min(os.cpu_count(), 4)),  # Limit threads
                "-y",                           # overwrite output
                output_audio_path
            ]
            
            logger.info(f"Extracting audio from {video_path}")
            start_time = time.time()
            
            result = subprocess.run(
                command, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE, 
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            elapsed = time.time() - start_time
            logger.info(f"Audio extraction completed in {elapsed:.2f}s")
            
            if result.returncode != 0:
                raise RuntimeError(f"FFmpeg error: {result.stderr}")
                
            return output_audio_path
            
        except subprocess.TimeoutExpired:
            raise Exception("Audio extraction timed out after 5 minutes")
        except Exception as e:
            raise Exception(f"Error extracting audio: {str(e)}")

    @staticmethod
    def create_smart_chunks(audio_path: str) -> Tuple[List[str], str]:
        """Create optimized chunks with better size management"""
        try:
            file_size = os.path.getsize(audio_path)
            file_size_mb = file_size / (1024 * 1024)
            temp_dir = tempfile.mkdtemp()
            
            logger.info(f"Audio file size: {file_size_mb:.2f} MB")
            
            # If file is small enough, don't chunk
            if file_size_mb <= PrdUploadService.MAX_CHUNK_SIZE_MB:
                logger.info("File small enough, processing as single chunk")
                return [audio_path], temp_dir
            
            # Calculate optimal chunk duration (aim for ~10MB chunks)
            duration_cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration", 
                "-of", "csv=p=0", audio_path
            ]
            
            try:
                duration_result = subprocess.run(duration_cmd, capture_output=True, text=True, timeout=30)
                total_duration = float(duration_result.stdout.strip())
                target_chunks = max(2, int(file_size_mb / PrdUploadService.MAX_CHUNK_SIZE_MB))
                chunk_duration = total_duration / target_chunks
                
                logger.info(f"Total duration: {total_duration:.2f}s, Target chunks: {target_chunks}")
                
            except (subprocess.TimeoutExpired, ValueError, subprocess.CalledProcessError):
                # Fallback to fixed duration
                chunk_duration = 600  # 10 minutes default
                logger.warning("Could not determine duration, using default chunk size")
            
            chunk_paths = []
            chunk_index = 0
            
            while True:
                start_time = chunk_index * chunk_duration
                chunk_path = os.path.join(temp_dir, f"chunk_{chunk_index:03d}.mp3")
                
                command = [
                    "ffmpeg",
                    "-i", audio_path,
                    "-ss", str(start_time),
                    "-t", str(chunk_duration),
                    "-ac", "1",
                    "-ar", "16000",
                    "-acodec", "libmp3lame",
                    "-b:a", "64k",
                    "-f", "mp3",
                    "-y",
                    chunk_path
                ]
                
                result = subprocess.run(
                    command, 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    timeout=60  # 1 minute timeout per chunk
                )
                
                if result.returncode == 0 and os.path.exists(chunk_path):
                    chunk_size = os.path.getsize(chunk_path)
                    chunk_size_mb = chunk_size / (1024 * 1024)
                    
                    if chunk_size > 50000:  # At least 50KB
                        if chunk_size_mb <= 24:  # Ensure within API limits
                            chunk_paths.append(chunk_path)
                            logger.info(f"Created chunk {chunk_index}: {chunk_size_mb:.2f} MB")
                            chunk_index += 1
                        else:
                            logger.warning(f"Chunk {chunk_index} too large ({chunk_size_mb:.2f} MB), skipping")
                            os.remove(chunk_path)
                            break
                    else:
                        os.remove(chunk_path)
                        break
                else:
                    break
            
            logger.info(f"Created {len(chunk_paths)} chunks")
            return chunk_paths, temp_dir
            
        except Exception as e:
            raise Exception(f"Error creating chunks: {str(e)}")

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=2, min=3, max=15))
    def transcribe_audio_chunk_with_retry(chunk_path: str) -> str:
        """Enhanced retry logic with better error handling"""
        try:
            file_size = os.path.getsize(chunk_path)
            file_size_mb = file_size / (1024 * 1024)
            
            if file_size == 0:
                logger.warning(f"Empty chunk file: {chunk_path}")
                return ""
            
            if file_size_mb > 25:  # OpenAI limit
                logger.error(f"Chunk too large ({file_size_mb:.1f}MB): {chunk_path}")
                return ""
            
            logger.info(f"Transcribing {os.path.basename(chunk_path)} ({file_size_mb:.1f}MB)")
            start_time = time.time()
            
            # Add timeout to OpenAI request
            client = openai.OpenAI(timeout=PrdUploadService.WHISPER_TIMEOUT)
            
            with open(chunk_path, "rb") as audio_file:
                result = client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    response_format="text",
                    language="en",
                    temperature=0
                )
            
            elapsed = time.time() - start_time
            transcript = result.strip() if result else ""
            logger.info(f"Transcribed {os.path.basename(chunk_path)}: {len(transcript)} chars in {elapsed:.2f}s")
            
            return transcript
            
        except Exception as e:
            logger.error(f"Transcription failed for {chunk_path}: {str(e)}")
            raise

    @staticmethod
    def sequential_transcribe_with_delays(chunk_paths: List[str]) -> List[str]:
        """Sequential processing with delays to avoid rate limits"""
        if not chunk_paths:
            return []
        
        logger.info(f"Starting sequential transcription of {len(chunk_paths)} chunks")
        transcripts = []
        
        for i, chunk_path in enumerate(chunk_paths):
            try:
                logger.info(f"Processing chunk {i+1}/{len(chunk_paths)}")
                transcript = PrdUploadService.transcribe_audio_chunk_with_retry(chunk_path)
                transcripts.append(transcript)
                
                # Add delay between requests to respect rate limits
                if i < len(chunk_paths) - 1:  # Don't delay after last chunk
                    delay = PrdUploadService.RETRY_DELAY + (i * 0.5)  # Increasing delay
                    logger.info(f"Waiting {delay:.1f}s before next request...")
                    time.sleep(delay)
                    
            except Exception as e:
                logger.error(f"Failed to transcribe chunk {i+1}: {str(e)}")
                transcripts.append("")
        
        successful_transcripts = [t for t in transcripts if t]
        logger.info(f"Sequential transcription completed: {len(successful_transcripts)}/{len(chunk_paths)} successful")
        return transcripts

    @staticmethod
    def clean_and_merge_transcripts_optimized(transcripts: List[str]) -> str:
        """Optimized transcript cleaning"""
        valid_transcripts = [t.strip() for t in transcripts if t and t.strip()]
        
        if not valid_transcripts:
            return "No valid transcripts found."
        
        # Simple and effective cleaning
        combined = " ".join(valid_transcripts)
        
        # Basic cleaning patterns
        import re
        
        # Remove excessive whitespace
        combined = re.sub(r'\s+', ' ', combined)
        
        # Fix sentence spacing
        combined = re.sub(r'\.(?=[A-Z])', '. ', combined)
        combined = re.sub(r'([.!?])\s*([A-Z])', r'\1 \2', combined)
        
        # Capitalize first letter
        if combined:
            combined = combined[0].upper() + combined[1:] if len(combined) > 1 else combined.upper()
        
        logger.info(f"Final transcript length: {len(combined)} characters")
        return combined

    @staticmethod
    def extract_text_from_video_optimized(video_path: str) -> str:
        """Optimized video processing with better resource management"""
        temp_audio_path = None
        temp_dir = None
        
        try:
            if not os.path.exists(video_path):
                raise Exception(f"Video file not found: {video_path}")
            
            # Check video file size first
            video_size_mb = os.path.getsize(video_path) / (1024 * 1024)
            logger.info(f"Processing video: {video_path} ({video_size_mb:.2f} MB)")
            
            if video_size_mb > 500:  # 500MB limit
                return "Video file too large for processing (max 500MB)"
            
            start_time = time.time()
            
            # Create temporary audio file with MP3 extension
            temp_audio_path = tempfile.mktemp(suffix=".mp3")
            PrdUploadService.extract_audio_from_video_optimized(video_path, temp_audio_path)
            
            # Process audio with smart chunking
            chunk_paths, temp_dir = PrdUploadService.create_smart_chunks(temp_audio_path)
            
            # Use sequential processing for better rate limit handling
            if len(chunk_paths) == 1 and chunk_paths[0] == temp_audio_path:
                logger.info("Processing as single file")
                transcript = PrdUploadService.transcribe_audio_chunk_with_retry(temp_audio_path)
                transcripts = [transcript] if transcript else []
            else:
                logger.info(f"Processing {len(chunk_paths)} chunks sequentially")
                transcripts = PrdUploadService.sequential_transcribe_with_delays(chunk_paths)
            
            final_transcript = PrdUploadService.clean_and_merge_transcripts_optimized(transcripts)
            
            elapsed = time.time() - start_time
            logger.info(f"Total video processing time: {elapsed:.2f}s")
            return final_transcript
            
        except Exception as e:
            logger.error(f"Error processing video {video_path}: {str(e)}")
            return f"Error processing video: {str(e)}"
        
        finally:
            # Enhanced cleanup
            cleanup_paths = []
            if temp_audio_path and os.path.exists(temp_audio_path):
                cleanup_paths.append(temp_audio_path)
            if temp_dir and os.path.exists(temp_dir):
                cleanup_paths.append(temp_dir)
            
            for path in cleanup_paths:
                try:
                    if os.path.isfile(path):
                        os.remove(path)
                    elif os.path.isdir(path):
                        shutil.rmtree(path)
                    logger.info(f"Cleaned up: {path}")
                except Exception as cleanup_error:
                    logger.warning(f"Cleanup error for {path}: {cleanup_error}")

    @staticmethod
    def extract_text_from_audio_openai_optimized(audio_path: str) -> str:
        """Optimized audio transcription"""
        try:
            file_size = os.path.getsize(audio_path)
            file_size_mb = file_size / (1024 * 1024)
            
            logger.info(f"Processing audio file: {audio_path} ({file_size_mb:.2f} MB)")
            
            if file_size_mb > PrdUploadService.MAX_CHUNK_SIZE_MB:
                chunk_paths, temp_dir = PrdUploadService.create_smart_chunks(audio_path)
                try:
                    transcripts = PrdUploadService.sequential_transcribe_with_delays(chunk_paths)
                    return PrdUploadService.clean_and_merge_transcripts_optimized(transcripts)
                finally:
                    if temp_dir and os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir)
            else:
                return PrdUploadService.transcribe_audio_chunk_with_retry(audio_path)
                
        except Exception as e:
            logger.error(f"Error processing audio {audio_path}: {str(e)}")
            return f"Error processing audio: {str(e)}"

    @staticmethod
    def extract_text_from_single_file(file_path: str, filename: str) -> str:
        """Extract text from a single file based on its extension"""
        ext = Path(file_path).suffix.lower()
        text = ""
        
        try:
            if ext == '.pdf':
                loader = PyPDFLoader(file_path)
                text = "\n".join([page.page_content for page in loader.load()])
            elif ext == '.docx':
                loader = Docx2txtLoader(file_path)
                docs = loader.load()
                text = "\n".join([doc.page_content for doc in docs])
            elif ext == '.txt':
                loader = TextLoader(file_path, encoding='utf-8')
                docs = loader.load()
                text = "\n".join([doc.page_content for doc in docs])
            elif ext in ['.jpg', '.jpeg', '.png']:
                image = Image.open(file_path)
                text = pytesseract.image_to_string(image)
            elif ext in ['.mp3', '.wav', '.m4a']:
                text = PrdUploadService.extract_text_from_audio_openai_optimized(file_path)
            elif ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']:
                text = PrdUploadService.extract_text_from_video_optimized(file_path)
            else:
                text = "Unsupported file format."
                
            if not text or text.strip() == "":
                text = "No readable content found in file."
                
        except Exception as e:
            logger.error(f"Error extracting text from {filename}: {str(e)}")
            text = f"Error extracting text from {filename}: {str(e)}"
            
        return text

    @staticmethod
    def normalize_headings(text: str) -> str:
        lines = text.splitlines()
        normalized = []
        
        for line in lines:
            line = line.strip()
            if re.match(r"^#+\s*\d+\..+", line):
                clean = re.sub(r"^#+\s*", "", line).strip()
                normalized.append(f"**{clean}**")
            elif re.match(r"^#+\s*[A-Za-z]", line):
                clean = re.sub(r"^#+\s*", "", line).strip()
                normalized.append(f"**{clean}**")
            elif re.match(r"\*\*\d+\..+\*\*", line):
                clean = re.sub(r"^\*{2}|\*{2}$", "", line).strip()
                normalized.append(f"**{clean}**")
            elif re.match(r"\*\*[A-Za-z\s\-()]+[:]*\*\*", line):
                clean = re.sub(r"^\*{2}|\*{2}$", "", line).strip()
                normalized.append(f"**{clean}**")
            else:
                normalized.append(line)
                
        return "\n".join(normalized)

    @staticmethod
    def save_temp_file(file: UploadFile) -> tuple:
        suffix = Path(file.filename).suffix
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as temp_file:
            content = file.file.read()
            temp_file.write(content)
            temp_file.flush()
            return Path(temp_file.name), content
    
    @staticmethod
    def process_image(file_name: str, content: bytes) -> str:
        ext = Path(file_name).suffix
        image_filename = f"{uuid4()}{ext}"
        relative_path = f"{image_filename}"
        full_path = Path("static/prd_image_uploads") / relative_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        with open(full_path, "wb") as f:
            f.write(content)
        return f"/static/prd_image_uploads/{relative_path}"
    
    @staticmethod
    def extract_and_format_text(file_path: Path, filename: str) -> str:
        text = PrdUploadService.extract_text_from_single_file(str(file_path), filename)
        return f"\n\n--- Start of {filename} ---\n{text}\n--- End of {filename} ---\n"
    
    @staticmethod
    def generate_prd_output(text: str, chat_history: List[BaseMessage], user_input: str) -> str:
        chat_template = ChatPromptTemplate.from_messages([
            ("system", "You are an AI assistant for writing PRDs from extracted documents."),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}")
        ])
        llm = ChatOpenAI(model="gpt-4o")
        persona_prompt = PromptTemplate(
            input_variables=["extracted_text"],
            template="""
            You are an expert in business analysis and technical documentation.

            Your task is to identify the most relevant professional role or persona the AI assistant should adopt to best generate a Project Requirement Document (PRD), based on the provided extracted project information.

            Your response must follow this exact format:
            "You are a [specific persona] with [X] years of experience"

            Examples:
            - "You are a Healthcare IT Consultant with 10 years of experience"
            - "You are an E-commerce AI Product Analyst with 15 years of experience"
            - "You are a Logistics Optimization Specialist with 12 years of experience"
            - "You are a Customer Experience AI Architect with 10 years of experience"

            Persona should be:
            - Specific to the industry or domain mentioned
            - Reflective of the type of problem being solved
            - Focused on the role needed to analyze and convert the input into a PRD

            Extracted Project Information:
            {extracted_text}
            """
        )
        template = load_prompt("prompt/template.json")
        persona_chain = persona_prompt | llm | StrOutputParser()
        persona = persona_chain.invoke({"extracted_text": text})
        chain = chat_template | llm | StrOutputParser()
        current_input = template.invoke({
            "extracted_text": text,
            "user_instruction": user_input if user_input else "None",
            "persona": persona
        }).text
        result = chain.invoke({"chat_history": chat_history, "input": current_input})
        return PrdUploadService.normalize_headings(result)

    @staticmethod
    def save_chat_to_db(db, session_id: str, chat_id: str, user_msg: str, ai_msg: str):
        now = datetime.now()
        db.add_all([
            ChatHistory(session_id=session_id, role="human", chat_id=chat_id, message=user_msg, created_at=now),
            ChatHistory(session_id=session_id, role="ai", chat_id=chat_id, message=ai_msg, created_at=now)
        ])
        db.commit()

    @staticmethod
    def process_uploaded_files(user_id: int, user_input: Optional[str], files: List[UploadFile], session_id: str, db: Session) -> dict:
        combined_text = ""
        failed_files = []
        uploaded_file_messages = []
        image_urls = []
        filenames = []
        chat_history = []
        chat_id = str(uuid4())
        
        # Get chat history
        chathistory = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id
        ).order_by(ChatHistory.created_at.asc()).limit(6).all()[::-1]
        
        for record in chathistory:
            if record.role == "human":
                chat_history.append(HumanMessage(content=record.message))
            elif record.role == "ai":
                chat_history.append(AIMessage(content=record.message))
        
        # Process uploaded files
        if files:
            logger.info(f"Processing {len(files)} files")
            for file in files:
                try:
                    filename = file.filename
                    filenames.append(filename)

                    temp_path, content = PrdUploadService.save_temp_file(file)

                    if Path(filename).suffix.lower() in ['.jpg', '.jpeg', '.png']:
                        image_url = PrdUploadService.process_image(filename, content)
                        image_urls.append(image_url)
                        uploaded_file_messages.append(f"![{filename}]({image_url})")
                    else:
                        uploaded_file_messages.append(filename)

                    combined_text += PrdUploadService.extract_and_format_text(temp_path, filename)

                except Exception as e:
                    logger.error(f"Failed to process file {filename}: {str(e)}")
                    failed_files.append({"filename": filename, "status": "failed", "error": str(e)})
                finally:
                    try:
                        os.unlink(temp_path)
                    except:
                        pass
                    file.file.seek(0)

        # Generate PRD result
        prd_result = PrdUploadService.generate_prd_output(combined_text.strip(), chat_history, user_input)
        
        # Prepare user message
        if uploaded_file_messages and user_input:
            user_msg = "Uploaded files:\n" + "\n".join(uploaded_file_messages) + "\n\n" + user_input
        elif uploaded_file_messages:
            user_msg = "Uploaded files:\n" + "\n".join(uploaded_file_messages)
        else:
            user_msg = user_input

        # Save to database
        PrdUploadService.save_chat_to_db(db, session_id, chat_id, user_msg, prd_result)

        # Invalidate caches
        CacheService.invalidate_chat_cache(user_id)
        CacheService.invalidate_chat_data_cache(user_id, session_id)
        
        return {
            "session_id": session_id,
            "prd_result": prd_result,
            "filenames": filenames,
            "user_input": user_input,
            "image_urls": image_urls,
            "failed_files": failed_files
        }